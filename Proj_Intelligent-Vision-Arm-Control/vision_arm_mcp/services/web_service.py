#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Web服务
Web Service

基于Flask的Web应用服务，提供：
- RESTful API接口
- 实时视频流
- Web界面
- 系统状态监控
"""

import cv2
import numpy as np
import logging
import time
from flask import Flask, Response, jsonify, request
from typing import Optional

from ..core.detection import YOLO11_Detect
from ..core.arm_control import RobotArm
from ..core.utils import MeasurementCollection, FPSCounter, draw_detections, draw_coordinate_system
from ..config.settings import Settings

logger = logging.getLogger("ShapeDetection")

class WebService:
    """Web应用类，提供视频流和API服务"""
    
    def __init__(self, model: YOLO11_Detect, config: Settings):
        """
        初始化Web应用
        
        Args:
            model: YOLO检测模型实例
            config: 配置对象
        """
        self.app = Flask(__name__)
        self.model = model
        self.config = config
        
        # 摄像头配置
        self.camera_path = config.camera.device_path
        self.width = config.camera.width
        self.height = config.camera.height
        
        # 设置摄像头
        self.setup_camera()
        
        # 创建FPS计数器
        self.fps_counter = FPSCounter(avg_frames=30)
        
        # 创建测量数据管理器
        self.measurement_collection = MeasurementCollection()

        # 初始化机械臂
        logger.info("正在初始化机械臂...")
        self.robot_arm = None
        try:
            self.robot_arm = RobotArm()
            logger.info("✓ 机械臂初始化成功")
        except Exception as e:
            logger.warning(f"⚠️ 机械臂初始化失败: {e}")
            logger.warning("将在无机械臂模式下运行")
        
        # 当前处理状态
        self.processing_stats = {
            'fps': 0,
            'inference_time': 0,
            'detection_count': 0,
            'last_update': time.time()
        }
        
        # 设置路由
        self.setup_routes()
        
        logger.info("Web服务初始化完成")
    
    def setup_camera(self):
        """设置摄像头"""
        try:
            self.camera = cv2.VideoCapture(self.camera_path)
            if not self.camera.isOpened():
                raise Exception(f"无法打开摄像头: {self.camera_path}")
            
            # 设置摄像头参数
            self.camera.set(cv2.CAP_PROP_FRAME_WIDTH, self.width)
            self.camera.set(cv2.CAP_PROP_FRAME_HEIGHT, self.height)
            self.camera.set(cv2.CAP_PROP_FPS, 30)
            
            # 获取实际设置的分辨率
            actual_width = self.camera.get(cv2.CAP_PROP_FRAME_WIDTH)
            actual_height = self.camera.get(cv2.CAP_PROP_FRAME_HEIGHT)
            logger.info(f"✓ 摄像头初始化成功: {self.camera_path}")
            logger.info(f"请求分辨率: {self.width}x{self.height}, 实际分辨率: {actual_width}x{actual_height}")
        except Exception as e:
            logger.error(f"❌ 摄像头初始化失败: {e}")
            self.camera = None
    
    def setup_routes(self):
        """设置Flask路由"""
        
        @self.app.route('/')
        def index():
            """主页"""
            return """
            <!DOCTYPE html>
            <html>
            <head>
                <title>智能视觉机械臂控制系统</title>
                <meta charset="utf-8">
            </head>
            <body>
                <h1>智能视觉机械臂控制系统</h1>
                <h2>实时视频流</h2>
                <img src="/video_feed" width="640" height="480">
                <h2>系统状态</h2>
                <div id="status"></div>
                <script>
                    function updateStatus() {
                        fetch('/api/status')
                            .then(response => response.json())
                            .then(data => {
                                document.getElementById('status').innerHTML =
                                    '<p>FPS: ' + data.fps.toFixed(2) + '</p>' +
                                    '<p>推理时间: ' + (data.inference_time * 1000).toFixed(2) + ' ms</p>' +
                                    '<p>检测数量: ' + data.detection_count + '</p>';
                            });
                    }
                    setInterval(updateStatus, 1000);
                    updateStatus();
                </script>
            </body>
            </html>
            """
        
        @self.app.route('/video_feed')
        def video_feed():
            """视频流"""
            return Response(
                self._generate_frames(),
                mimetype='multipart/x-mixed-replace; boundary=frame'
            )
        
        @self.app.route('/api/status')
        def api_status():
            """获取处理状态"""
            return jsonify(self.processing_stats)
        
        @self.app.route('/api/measurements')
        def api_measurements():
            """获取所有物体的测量数据"""
            try:
                measurements = self.measurement_collection.get_all()
                return jsonify({
                    'count': len(measurements),
                    'timestamp': self.measurement_collection.last_update,
                    'measurements': measurements
                })
            except Exception as e:
                logger.error(f"获取测量数据时出错: {str(e)}")
                return jsonify({'error': f'获取测量数据失败: {str(e)}'}), 500
        
        @self.app.route('/api/measurements/<int:object_id>')
        def api_measurement_by_id(object_id):
            """获取特定ID的物体测量数据"""
            try:
                measurement = self.measurement_collection.get_by_id(object_id)
                if measurement:
                    return jsonify(measurement)
                else:
                    return jsonify({'error': f'未找到ID为{object_id}的测量数据'}), 404
            except Exception as e:
                logger.error(f"获取ID为{object_id}的测量数据时出错: {str(e)}")
                return jsonify({'error': f'获取测量数据失败: {str(e)}'}), 500
        
        @self.app.route('/api/arm/catch_object', methods=['POST'])
        def api_arm_catch_object():
            """控制机械臂抓取物体"""
            if not self.robot_arm:
                return jsonify({'error': '机械臂未初始化'}), 503
            
            try:
                data = request.get_json()
                x = data.get('x')
                y = data.get('y')
                z = data.get('z', 50)  # 默认高度
                
                if x is None or y is None:
                    return jsonify({'error': '缺少必要参数 x, y'}), 400
                
                # 执行抓取动作
                success = self.robot_arm.arm_catch_sth(x, y, z)
                
                if success:
                    return jsonify({'message': f'成功抓取位置 ({x}, {y}, {z}) 的物体'})
                else:
                    return jsonify({'error': '抓取动作执行失败'}), 500
                    
            except Exception as e:
                logger.error(f"机械臂抓取时出错: {str(e)}")
                return jsonify({'error': f'抓取失败: {str(e)}'}), 500
        
        @self.app.route('/api/arm/put_object', methods=['POST'])
        def api_arm_put_object():
            """控制机械臂放置物体"""
            if not self.robot_arm:
                return jsonify({'error': '机械臂未初始化'}), 503
            
            try:
                data = request.get_json()
                x = data.get('x')
                y = data.get('y')
                z = data.get('z', 50)  # 默认高度
                
                if x is None or y is None:
                    return jsonify({'error': '缺少必要参数 x, y'}), 400
                
                # 执行放置动作
                success = self.robot_arm.arm_normal_put_sth(x, y, z)
                
                if success:
                    return jsonify({'message': f'成功将物体放置到位置 ({x}, {y}, {z})'})
                else:
                    return jsonify({'error': '放置动作执行失败'}), 500
                    
            except Exception as e:
                logger.error(f"机械臂放置时出错: {str(e)}")
                return jsonify({'error': f'放置失败: {str(e)}'}), 500

    def _generate_frames(self):
        """生成视频帧"""
        while True:
            if not self.camera or not self.camera.isOpened():
                # 如果摄像头不可用，生成空白帧
                blank_frame = np.zeros((480, 640, 3), dtype=np.uint8)
                cv2.putText(blank_frame, "Camera not available", (50, 240),
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
                ret, buffer = cv2.imencode('.jpg', blank_frame)
                frame = buffer.tobytes()
                yield (b'--frame\r\n'
                       b'Content-Type: image/jpeg\r\n\r\n' + frame + b'\r\n')
                time.sleep(0.1)
                continue

            success, frame = self.camera.read()
            if not success:
                continue

            try:
                # 执行检测
                ids, scores, bboxes, inference_time = self.model.process_frame(frame)

                # 获取类别名称
                class_names = [self.model.class_names[int(id)]
                              if int(id) < len(self.model.class_names)
                              else f"类别{id}" for id in ids]

                # 更新测量数据
                frame_height, frame_width = frame.shape[:2]
                self.measurement_collection.update(
                    class_ids=ids,
                    class_names=class_names,
                    scores=scores,
                    bboxes=bboxes,
                    frame_width=frame_width,
                    frame_height=frame_height
                )

                # 绘制检测结果
                detection_config = {
                    'class_names': self.config.detection.class_names,
                    'colors': self.config.detection.colors,
                    'font_scale': self.config.detection.font_scale,
                    'line_thickness': self.config.detection.line_thickness
                }
                measurement_config = {
                    'enable_coordinate_system': self.config.measurement.enable_coordinate_system,
                    'enable_distance_measurement': self.config.measurement.enable_distance_measurement,
                    'enable_dimension_measurement': self.config.measurement.enable_dimension_measurement,
                    'axis_color': self.config.measurement.axis_color,
                    'measurement_color': self.config.measurement.measurement_color,
                    'axis_length_percentage': self.config.measurement.axis_length_percentage,
                    'axis_thickness': self.config.measurement.axis_thickness,
                    'measurement_line_thickness': self.config.measurement.measurement_line_thickness
                }
                frame = draw_detections(frame, ids, scores, bboxes,
                                      detection_config=detection_config,
                                      measurement_config=measurement_config)

                # 更新FPS
                self.fps_counter.update()

                # 更新处理状态
                self.processing_stats.update({
                    'fps': self.fps_counter.get_fps(),
                    'inference_time': inference_time,
                    'detection_count': len(ids),
                    'last_update': time.time()
                })

                # 编码帧
                ret, buffer = cv2.imencode('.jpg', frame)
                frame = buffer.tobytes()

                yield (b'--frame\r\n'
                       b'Content-Type: image/jpeg\r\n\r\n' + frame + b'\r\n')

            except Exception as e:
                logger.error(f"处理帧时出错: {e}")
                continue

    def run(self):
        """运行Web服务"""
        logger.info(f"启动Web服务: http://{self.config.web.host}:{self.config.web.port}")

        self.app.run(
            host=self.config.web.host,
            port=self.config.web.port,
            debug=self.config.web.debug,
            threaded=True,
            use_reloader=False  # 避免在统一服务器中重载
        )

    def stop(self):
        """停止Web服务"""
        # 释放摄像头资源
        if hasattr(self, 'camera') and self.camera and self.camera.isOpened():
            self.camera.release()
            logger.info("摄像头资源已释放")

        # 关闭机械臂连接
        if self.robot_arm:
            try:
                # 假设机械臂有close方法
                if hasattr(self.robot_arm, 'close'):
                    self.robot_arm.close()
                logger.info("机械臂连接已关闭")
            except Exception as e:
                logger.warning(f"关闭机械臂连接时出错: {e}")

        logger.info("Web服务已停止")
