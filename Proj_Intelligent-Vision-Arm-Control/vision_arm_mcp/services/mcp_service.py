#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
MCP服务
MCP Service

基于FastMCP的MCP协议服务器，提供：
- AI助手集成接口
- 异步HTTP客户端
- 工具函数封装
- 生命周期管理
"""

import logging
import asyncio
from contextlib import asynccontextmanager
from typing import Dict, Any, Optional

import httpx
from fastmcp import FastMCP

from ..config.settings import Settings

logger = logging.getLogger("MCPService")

class MCPService:
    """MCP服务类"""
    
    def __init__(self, web_service_url: str, config: Settings):
        """
        初始化MCP服务
        
        Args:
            web_service_url: Web服务的基础URL
            config: 系统配置
        """
        self.web_service_url = web_service_url.rstrip('/')
        self.config = config
        self.http_client: Optional[httpx.AsyncClient] = None
        
        # 创建FastMCP实例
        self.mcp = FastMCP(
            name=config.mcp.name,
            lifespan=self._lifespan
        )
        
        # 注册MCP工具
        self._register_tools()
        
        logger.info("MCP服务初始化完成")
    
    @asynccontextmanager
    async def _lifespan(self, app):
        """MCP应用生命周期管理"""
        # 启动时创建HTTP客户端
        self.http_client = httpx.AsyncClient(timeout=30.0)
        logger.info("MCP HTTP客户端已创建")
        
        try:
            yield
        finally:
            # 关闭时释放HTTP客户端
            if self.http_client:
                await self.http_client.aclose()
                logger.info("MCP HTTP客户端已关闭")
    
    def _register_tools(self):
        """注册MCP工具函数"""
        
        @self.mcp.tool
        async def get_measurements() -> Dict[str, Any]:
            """获取所有检测物体的测量数据"""
            try:
                response = await self.http_client.get(f"{self.web_service_url}/api/measurements")
                response.raise_for_status()
                return response.json()
            except httpx.HTTPError as e:
                logger.error(f"获取测量数据失败: {e}")
                return {"error": f"HTTP error occurred: {e}"}
            except Exception as e:
                logger.error(f"获取测量数据异常: {e}")
                return {"error": f"An error occurred: {e}"}
        
        @self.mcp.tool
        async def get_measurement_by_id(object_id: int) -> Dict[str, Any]:
            """
            获取特定物体的测量数据
            
            Args:
                object_id: 物体ID
            """
            try:
                response = await self.http_client.get(f"{self.web_service_url}/api/measurements/{object_id}")
                response.raise_for_status()
                return response.json()
            except httpx.HTTPError as e:
                logger.error(f"获取物体 {object_id} 测量数据失败: {e}")
                return {"error": f"HTTP error occurred: {e}"}
            except Exception as e:
                logger.error(f"获取物体 {object_id} 测量数据异常: {e}")
                return {"error": f"An error occurred: {e}"}
        
        @self.mcp.tool
        async def catch_object(image_x: float, image_y: float, height_z: float) -> Dict[str, Any]:
            """
            控制机械臂抓取物体
            
            Args:
                image_x: 图像坐标X (像素)
                image_y: 图像坐标Y (像素)  
                height_z: 抓取高度Z (毫米)
            """
            try:
                payload = {
                    "image_x": image_x,
                    "image_y": image_y, 
                    "height_z": height_z
                }
                response = await self.http_client.post(
                    f"{self.web_service_url}/api/arm/catch_object",
                    json=payload
                )
                response.raise_for_status()
                result = response.json()
                logger.info(f"抓取物体成功: {payload}")
                return result
            except httpx.HTTPError as e:
                logger.error(f"抓取物体HTTP错误: {e}")
                return {"error": f"HTTP error occurred: {e}"}
            except Exception as e:
                logger.error(f"抓取物体异常: {e}")
                return {"error": f"An error occurred: {e}"}
        
        @self.mcp.tool
        async def put_object(image_x: float, image_y: float, height_z: float) -> Dict[str, Any]:
            """
            控制机械臂放置物体
            
            Args:
                image_x: 图像坐标X (像素)
                image_y: 图像坐标Y (像素)
                height_z: 放置高度Z (毫米)
            """
            try:
                payload = {
                    "image_x": image_x,
                    "image_y": image_y,
                    "height_z": height_z
                }
                response = await self.http_client.post(
                    f"{self.web_service_url}/api/arm/put_object", 
                    json=payload
                )
                response.raise_for_status()
                result = response.json()
                logger.info(f"放置物体成功: {payload}")
                return result
            except httpx.HTTPError as e:
                logger.error(f"放置物体HTTP错误: {e}")
                return {"error": f"HTTP error occurred: {e}"}
            except Exception as e:
                logger.error(f"放置物体异常: {e}")
                return {"error": f"An error occurred: {e}"}
        
        @self.mcp.tool
        async def get_system_status() -> Dict[str, Any]:
            """获取系统状态信息"""
            try:
                response = await self.http_client.get(f"{self.web_service_url}/api/status")
                response.raise_for_status()
                return response.json()
            except httpx.HTTPError as e:
                logger.error(f"获取系统状态失败: {e}")
                return {"error": f"HTTP error occurred: {e}"}
            except Exception as e:
                logger.error(f"获取系统状态异常: {e}")
                return {"error": f"An error occurred: {e}"}
        
        @self.mcp.tool
        async def get_available_tools() -> Dict[str, Any]:
            """获取可用的MCP工具列表"""
            tools = [
                {
                    "name": "get_measurements",
                    "description": "获取所有检测物体的测量数据",
                    "parameters": []
                },
                {
                    "name": "get_measurement_by_id", 
                    "description": "获取特定物体的测量数据",
                    "parameters": ["object_id: int"]
                },
                {
                    "name": "catch_object",
                    "description": "控制机械臂抓取物体",
                    "parameters": ["image_x: float", "image_y: float", "height_z: float"]
                },
                {
                    "name": "put_object",
                    "description": "控制机械臂放置物体", 
                    "parameters": ["image_x: float", "image_y: float", "height_z: float"]
                },
                {
                    "name": "get_system_status",
                    "description": "获取系统状态信息",
                    "parameters": []
                }
            ]
            
            return {
                "tools": tools,
                "count": len(tools),
                "server_name": self.config.mcp.name,
                "server_description": self.config.mcp.description
            }
        
        logger.info("MCP工具函数注册完成")
    
    async def run(self):
        """运行MCP服务"""
        try:
            logger.info(f"启动MCP服务: http://{self.config.mcp.host}:{self.config.mcp.port}")

            # 使用FastMCP的run_async方法
            await self.mcp.run_async(
                transport="http",
                host=self.config.mcp.host,
                port=self.config.mcp.port
            )

        except Exception as e:
            logger.error(f"MCP服务运行错误: {e}")
            raise
    
    async def stop(self):
        """停止MCP服务"""
        logger.info("MCP服务正在停止...")
        # FastMCP的停止逻辑会在lifespan中处理
        logger.info("MCP服务已停止")
    
    def get_server_info(self) -> Dict[str, Any]:
        """获取MCP服务器信息"""
        return {
            "name": self.config.mcp.name,
            "description": self.config.mcp.description,
            "host": self.config.mcp.host,
            "port": self.config.mcp.port,
            "web_service_url": self.web_service_url,
            "tools_count": 6  # 当前注册的工具数量
        }
