# 2025-07-30
refactor(architecture): 重构项目架构为统一MCP服务器，优化配置管理和测试体系

基于之前的MCP服务器功能，本次更新进行了全面的架构重构，将项目转换为统一的MCP服务器架构，并大幅简化了配置管理和测试流程。

### 1. 架构重构 (refactor)
- **统一服务器架构**: 删除独立的 `fastmcp_server.py` 和 `main.py`，创建新的 `vision_arm_mcp/` 模块化架构
- **模块化设计**: 重新组织代码结构，将 `src/` 目录重构为 `vision_arm_mcp/` 包，包含核心逻辑、服务层、API层和配置管理
- **服务集成**: 在 `vision_arm_mcp/main.py` 中实现统一入口点，同时管理Web服务和MCP服务的生命周期
- **代码迁移**: 将所有核心模块从 `src/` 迁移到 `vision_arm_mcp/core/`，保持功能完整性

### 2. 配置管理优化 (feat)
- **配置简化**: 删除多个配置文件，统一使用 `config/default.yaml` 作为唯一配置文件
- **配置加载器**: 新增 `vision_arm_mcp/config/config_loader.py`，提供统一的配置加载和验证
- **类型安全**: 使用Pydantic进行配置数据验证和类型检查
- **日志优化**: 实现双级日志系统，文件记录全部日志，终端仅显示重要信息，并支持启动时清空日志文件

### 3. 服务层重构 (feat)
- **Web服务**: 重构 `vision_arm_mcp/services/web_service.py`，集成Flask应用和视频流处理
- **MCP服务**: 新增 `vision_arm_mcp/services/mcp_service.py`，基于FastMCP实现完整的MCP协议支持
- **视频服务**: 独立的 `vision_arm_mcp/services/video_service.py`，专门处理视频流和检测逻辑
- **API路由**: 新增 `vision_arm_mcp/api/routes.py` 和 `vision_arm_mcp/api/schemas.py`，提供结构化的API定义

### 4. 网络访问增强 (feat)
- **动态IP检测**: 实现自动检测所有网络接口，支持局域网访问
- **多接口显示**: 启动时显示所有可用的访问地址，包括localhost和LAN IP
- **网络配置**: 优化Web服务配置，支持0.0.0.0绑定以允许外部访问

### 5. 测试体系完善 (feat)
- **连通性测试**: 新增 `bin/test-services.sh`，提供全面的服务连通性测试
- **端点验证**: 测试所有HTTP端点、MCP协议连接和端口状态
- **智能检测**: 自动从配置文件读取端口信息，支持跨平台兼容
- **测试文档**: 新增 `docs/TESTING.md`，提供详细的测试指南和故障排除说明

### 6. 部署和运维 (feat)
- **启动脚本**: 新增 `bin/start-server.sh` 和 `bin/stop-server.sh`，简化服务管理
- **统一测试**: 新增 `tests/test_unified_server.py`，替代原有的分散测试文件
- **文档完善**: 新增多个文档文件，包括架构说明、配置指南、网络访问和清理报告

### 7. 代码清理 (chore)
- **删除冗余**: 移除旧的 `src/` 目录结构、独立的MCP服务器文件和过时的测试脚本
- **依赖更新**: 更新 `requirements.txt`，确保所有必要依赖的正确版本
- **文档更新**: 更新 `README.md`，反映新的架构和使用方式

### 8. 破坏性变更 (BREAKING CHANGE)
- **启动方式变更**: 原有的 `python main.py` 和 `python fastmcp_server.py` 启动方式已废弃
- **新启动方式**: 使用 `./bin/start-server.sh` 或 `python -m vision_arm_mcp.main` 启动统一服务器
- **配置文件变更**: 不再支持多环境配置文件，统一使用 `config/default.yaml`
- **API路径变更**: 部分API路径可能发生变化，请参考新的API文档

---

# 2025-07-29
feat(mcp): 重新集成MCP服务器功能，使用fastmcp包实现

在之前的提交中，我们撤销了MCP服务器功能集成，因为使用了错误的mcp python包。本次更新重新集成了MCP服务器功能，使用了正确的fastmcp包。

### 1. 新功能 (feat)
- **MCP服务器**: 新增 `fastmcp_server.py` 脚本，它使用 `FastMCP` 库将本项目的核心RESTful API（如 `/api/measurements`, `/api/arm/catch_object`）封装成MCP工具。
- **网络服务**: MCP服务器被配置为通过SSE（Server-Sent Events）在8002端口上运行，使其可以作为独立的网络服务被客户端连接。
- **测试脚本**: 新增 `test/test_mcp_server.py` 脚本，用于测试MCP服务器是否正常运行，包括启动服务器、连接服务器以及调用各个工具的测试。

### 2. 构建与环境 (chore)
- **依赖更新**: 在 `requirements.txt` 中添加了 `fastmcp` 依赖，以支持MCP服务器的运行。

---

# 2025-07-29
revert: 撤销MCP服务器功能集成

由于用错了mcp的python包，后续计划替换为fastmcp包。故本次更新是为了撤销于 2025-07-08 集成的MCP服务器功能。

### 1. 代码回滚 (revert)
- **删除MCP服务器**: 移除了 `vision_arm_server.py` 文件，该文件用于将项目API封装为MCP工具。
- **依赖清理**: 从 `requirements.txt` 中删除了 `httpx` 和 `mcp[cli]` 依赖，这些是MCP服务器所需的库。

---

# 2025-07-08
feat(mcp): 集成MCP服务，适配大语言模型Function Calling

为了将智能视觉机械臂的功能暴露给大语言模型，本次更新引入了模型上下文协议（MCP）的支持。通过创建一个独立的MCP服务器，本项目的核心API能够以工具（Tools）的形式被外部系统（如LLM）发现和调用。

### 1. 新功能 (feat)
- **MCP服务器**: 新增 `vision_arm_server.py` 脚本，它使用 `FastMCP` 库将本项目的核心RESTful API（如 `/api/measurements`, `/api/arm/catch_object`）封装成MCP工具。
- **网络服务**: MCP服务器被配置为通过SSE（Server-Sent Events）在8002端口上运行，使其可以作为独立的网络服务被客户端连接。

### 2. 构建与环境 (chore)
- **依赖更新**: 在 `requirements.txt` 中添加了 `httpx` 和 `mcp[cli]` 依赖，以支持MCP服务器的运行和网络通信。

---

# 2025-06-24
feat(project): 初始化智能视觉机械臂控制项目，建立基础框架和文档

本次提交是项目的首次提交，完成了从零到一的搭建，包括核心功能模块的初始化、基础文档的创建以及项目构建环境的配置。

### 1. 新功能 (feat)
- **核心模块**: 搭建了项目的基础框架，包括图像处理、机械臂控制和目标检测等核心功能模块的初步实现。
- **接口定义**: 定义了主要的类和函数接口，为后续的模块开发奠定了基础。

### 2. 文档 (docs)
- **README**: 创建了 `README.md` 文件，详细描述了项目目标、功能、安装指南和基本使用方法。
- **文档修正**: 对初始文档进行了校对和格式化，提升了可读性。

### 3. 构建与环境 (chore)
- **依赖管理**: 创建了 `requirements.txt` 文件，明确了项目运行所需的全部Python依赖库。
- **启动脚本**: 编写了基础的启动脚本，简化了项目的启动和测试流程。

---