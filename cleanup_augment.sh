#!/bin/bash

# ==============================================================================
#  脚本名称: cleanup_augment_final.sh
#  功能描述: [全自动] 彻底清理 "Augment Code" 插件的所有已知残留，并进行最终验证。
#  作者:     AI Assistant
# ==============================================================================

# --- 配置 ---
# 使用 "$HOME" 代替 "~" 以确保在所有 shell 环境中都能正确解析用户主目录
CURSOR_SERVER_DIR="$HOME/.cursor-server"
SETTINGS_FILE="$CURSOR_SERVER_DIR/data/User/settings.json"

echo "==============================================="
echo "==  'Augment Code' 插件终极清理脚本  =="
echo "==============================================="
echo ""
echo "警告：此脚本将永久删除文件并自动修改JSON配置文件。"
echo "配置文件将自动备份，以防万一。"
read -p "是否继续执行? (y/n): " -n 1 -r
echo # 移动到新行
if [[ ! $REPLY =~ ^[Yy]$ ]]
then
    echo "操作已取消。"
    exit 1
fi
echo ""

# --- 函数定义 ---
# 安全地删除目录或文件
delete_path() {
    local path_to_delete=$1
    if [ -e "$path_to_delete" ]; then # -e 检查文件或目录是否存在
        echo "正在删除: $path_to_delete"
        rm -rf "$path_to_delete"
        echo "  [成功] 已删除。"
    else
        echo "路径不存在，跳过: $path_to_delete"
    fi
}


# --- 1. 开始清理已知目录 ---
echo "--- 步骤 1/3: 清理插件主程序、存储和日志 ---"
delete_path "$CURSOR_SERVER_DIR/extensions/augment.vscode-augment-0.509.0"
delete_path "$CURSOR_SERVER_DIR/data/User/globalStorage/augment.vscode-augment"
delete_path "$CURSOR_SERVER_DIR/data/User/workspaceStorage/005489aa39167aee70ff448734a68856/Augment.vscode-augment"
delete_path "$CURSOR_SERVER_DIR/data/User/workspaceStorage/49454d2d687f519cc55f77de18bc43c9/Augment.vscode-augment"

# 清理所有相关的日志目录
shopt -s nullglob
log_dirs=($CURSOR_SERVER_DIR/data/logs/*/exthost*/Augment.vscode-augment)
shopt -u nullglob # 恢复默认行为
if [ ${#log_dirs[@]} -gt 0 ]; then
    echo "正在删除匹配的日志目录..."
    rm -rf $CURSOR_SERVER_DIR/data/logs/*/exthost*/Augment.vscode-augment
    echo "  [成功] 已删除所有相关日志。"
else
    echo "未找到匹配的日志目录，跳过。"
fi
echo ""


# --- 2. 自动清理配置文件 ---
echo "--- 步骤 2/3: 自动清理 settings.json 配置文件 ---"
if [ -f "$SETTINGS_FILE" ]; then
    # 检查文件中是否包含 "augment." 相关的配置
    if grep -q '"augment\.' "$SETTINGS_FILE"; then
        # 创建一个带时间戳的备份
        SETTINGS_BACKUP="$SETTINGS_FILE.bak-$(date +%s)"
        echo "发现相关配置，正在处理 $SETTINGS_FILE"
        echo "  -> 创建备份文件: $SETTINGS_BACKUP"
        cp "$SETTINGS_FILE" "$SETTINGS_BACKUP"

        # 使用 grep -v 过滤掉包含 "augment." 的行，并保存到临时文件
        grep -v '"augment\.' "$SETTINGS_FILE" > "$SETTINGS_FILE.tmp"
        
        # 将临时文件移回原处
        mv "$SETTINGS_FILE.tmp" "$SETTINGS_FILE"
        echo "  [成功] 已自动移除相关配置行。"
        echo "  [提示] 如果编辑器配置出现问题，请从备份文件恢复。"
    else
        echo "配置文件中未找到 'augment.' 相关配置，无需操作。"
    fi
else
    echo "配置文件不存在，跳过: $SETTINGS_FILE"
fi
echo ""


# --- 3. 最终验证 ---
echo "--- 步骤 3/3: 最终验证 ---"
echo "正在全盘扫描，以确认是否还有残留（将忽略 .npm 目录）..."

# -not -path "*npm*" 用于排除已知的、通常无关的npm缓存文件
REMAINING_FILES=$(find "$HOME" -iname "*augment*" -not -path "*npm*")

if [ -z "$REMAINING_FILES" ]; then
    echo ""
    echo "*****************************************************"
    echo "**  [完美] 清理成功！未发现任何相关残留文件。  **"
    echo "*****************************************************"
else
    echo ""
    echo "*****************************************************"
    echo "**  [警告] 清理完毕，但仍发现以下可能的残留文件: **"
    echo "*****************************************************"
    echo "$REMAINING_FILES"
    echo "*****************************************************"
    echo "这些文件可能需要您手动检查并决定是否删除。"
fi
echo ""
echo "所有操作已完成。"

