"use client"

/**
 * 工具调用显示组件
 * 支持收纳/展开功能和状态显示
 */

import { useState, useRef, useEffect } from "react"
import { ChevronDown, ChevronRight, Clock, CheckCircle, XCircle, Loader2, ArrowDown } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ToolCall, ToolCallStatus } from "@/lib/app-state-manager"

interface ToolCallDisplayProps {
  toolCall: ToolCall
  className?: string
}

export function ToolCallDisplay({ toolCall, className = "" }: ToolCallDisplayProps) {
  // 默认收纳状态，只有在有结果时才可能展开
  const [isExpanded, setIsExpanded] = useState(false)
  const [showScrollHint, setShowScrollHint] = useState(false)
  const resultRef = useRef<HTMLDivElement>(null)

  // 检查是否需要显示滚动提示
  useEffect(() => {
    if (isExpanded && resultRef.current && toolCall.result) {
      const element = resultRef.current
      const hasScroll = element.scrollHeight > element.clientHeight
      setShowScrollHint(hasScroll)
    }
  }, [isExpanded, toolCall.result])

  // 使用工具调用的状态字段
  const status = toolCall.status

  // 状态图标和文本
  const getStatusDisplay = () => {
    switch (status) {
      case 'pending':
        return {
          icon: <Loader2 className="h-3 w-3 animate-spin" />,
          text: '调用中',
          color: 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200'
        }
      case 'success':
        return {
          icon: <CheckCircle className="h-3 w-3" />,
          text: '成功',
          color: 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200'
        }
      case 'error':
        return {
          icon: <XCircle className="h-3 w-3" />,
          text: '失败',
          color: 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200'
        }
      default:
        return {
          icon: <Clock className="h-3 w-3" />,
          text: '等待中',
          color: 'bg-gray-100 dark:bg-gray-900 text-gray-800 dark:text-gray-200'
        }
    }
  }

  const statusDisplay = getStatusDisplay()

  return (
    <div className={`border rounded-lg bg-background/50 shadow-sm transition-all duration-200 hover:shadow-md ${className}`}>
      {/* 工具调用头部 - 始终显示 */}
      <div
        className="flex items-center justify-between p-3 cursor-pointer hover:bg-background/80 transition-colors rounded-lg"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center space-x-2 flex-1">
          {/* 展开/收纳图标 */}
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0"
            onClick={(e) => {
              e.stopPropagation()
              setIsExpanded(!isExpanded)
            }}
          >
            {isExpanded ? (
              <ChevronDown className="h-4 w-4" />
            ) : (
              <ChevronRight className="h-4 w-4" />
            )}
          </Button>

          {/* 工具名称 */}
          <div className="flex items-center space-x-2">
            <Badge variant="secondary" className="text-xs">
              工具
            </Badge>
            <span className="font-medium text-sm">{toolCall.name}</span>
          </div>
        </div>

        {/* 状态显示 */}
        <Badge className={`${statusDisplay.color} flex items-center space-x-1`}>
          {statusDisplay.icon}
          <span className="text-xs">{statusDisplay.text}</span>
        </Badge>
      </div>

      {/* 详细信息 - 展开时显示 */}
      <div className={`transition-all duration-300 ease-in-out ${
        isExpanded ? 'max-h-[800px] opacity-100' : 'max-h-0 opacity-0 overflow-hidden'
      }`}>
        <div className="border-t bg-background/30">
          {/* 参数信息 */}
          <div className="p-3">
            <div className="text-xs font-medium text-gray-500 mb-2">调用参数:</div>
            <div className="bg-gray-50 dark:bg-gray-800 p-2 rounded border text-xs max-h-48 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-400 dark:scrollbar-thumb-gray-600 scrollbar-track-transparent">
              <pre className="whitespace-pre-wrap text-gray-700 dark:text-gray-300 break-words">
                {JSON.stringify(toolCall.args, null, 2)}
              </pre>
            </div>
          </div>

          {/* 结果信息 */}
          {toolCall.result && status === 'success' && (
            <div className="p-3 pt-0">
              <div className="flex items-center justify-between mb-2">
                <div className="text-xs font-medium text-gray-500">执行结果:</div>
                {showScrollHint && (
                  <div className="flex items-center text-xs text-gray-400">
                    <ArrowDown className="h-3 w-3 mr-1" />
                    <span>可滚动查看更多</span>
                  </div>
                )}
              </div>
              <div
                ref={resultRef}
                className="bg-green-50 dark:bg-green-900/20 p-2 rounded border border-green-200 dark:border-green-800 text-xs max-h-64 overflow-y-auto scrollbar-thin scrollbar-thumb-green-300 dark:scrollbar-thumb-green-700 scrollbar-track-transparent"
              >
                <pre className="whitespace-pre-wrap text-green-700 dark:text-green-300 break-words">
                  {toolCall.result}
                </pre>
              </div>
            </div>
          )}

          {/* 错误信息 */}
          {toolCall.error && status === 'error' && (
            <div className="p-3 pt-0">
              <div className="text-xs font-medium text-gray-500 mb-2">错误信息:</div>
              <div className="bg-red-50 dark:bg-red-900/20 p-2 rounded border border-red-200 dark:border-red-800 text-xs max-h-64 overflow-y-auto scrollbar-thin scrollbar-thumb-red-300 dark:scrollbar-thumb-red-700 scrollbar-track-transparent">
                <pre className="whitespace-pre-wrap text-red-700 dark:text-red-300 break-words">
                  {toolCall.error}
                </pre>
              </div>
            </div>
          )}

          {/* 调用中的提示 */}
          {status === 'pending' && (
            <div className="p-3 pt-0">
              <div className="bg-yellow-50 dark:bg-yellow-900/20 p-2 rounded border border-yellow-200 dark:border-yellow-800 text-xs">
                <div className="flex items-center space-x-2 text-yellow-700 dark:text-yellow-300">
                  <Loader2 className="h-3 w-3 animate-spin" />
                  <span>正在执行工具调用，请稍候...</span>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

/**
 * 工具调用列表组件
 * 显示多个工具调用
 */
interface ToolCallListProps {
  toolCalls: ToolCall[]
  className?: string
}

export function ToolCallList({ toolCalls, className = "" }: ToolCallListProps) {
  if (!toolCalls || toolCalls.length === 0) {
    return null
  }

  return (
    <div className={`space-y-2 ${className}`}>
      <div className="text-xs font-medium text-gray-500 mb-2">
        工具调用 ({toolCalls.length})
      </div>
      {toolCalls.map((toolCall) => (
        <ToolCallDisplay 
          key={toolCall.id} 
          toolCall={toolCall}
        />
      ))}
    </div>
  )
}

/**
 * 工具调用摘要组件
 * 在收纳状态下显示简要信息
 */
interface ToolCallSummaryProps {
  toolCalls: ToolCall[]
  onExpand?: () => void
  className?: string
}

export function ToolCallSummary({ toolCalls, onExpand, className = "" }: ToolCallSummaryProps) {
  if (!toolCalls || toolCalls.length === 0) {
    return null
  }

  const pendingCount = toolCalls.filter(tool => tool.status === 'pending').length
  const successCount = toolCalls.filter(tool => tool.status === 'success').length
  const errorCount = toolCalls.filter(tool => tool.status === 'error').length

  return (
    <div 
      className={`flex items-center justify-between p-2 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded cursor-pointer hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors ${className}`}
      onClick={onExpand}
    >
      <div className="flex items-center space-x-2">
        <Badge variant="secondary" className="text-xs">
          工具
        </Badge>
        <span className="text-sm text-blue-700 dark:text-blue-300">
          {toolCalls.length}个工具调用
        </span>
      </div>

      <div className="flex items-center space-x-2">
        {pendingCount > 0 && (
          <Badge className="bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 flex items-center space-x-1">
            <Loader2 className="h-3 w-3 animate-spin" />
            <span className="text-xs">{pendingCount}个执行中</span>
          </Badge>
        )}
        {successCount > 0 && (
          <Badge className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 flex items-center space-x-1">
            <CheckCircle className="h-3 w-3" />
            <span className="text-xs">{successCount}个完成</span>
          </Badge>
        )}
        {errorCount > 0 && (
          <Badge className="bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 flex items-center space-x-1">
            <XCircle className="h-3 w-3" />
            <span className="text-xs">{errorCount}个失败</span>
          </Badge>
        )}
        <ChevronRight className="h-4 w-4 text-blue-500" />
      </div>
    </div>
  )
}
