#!/usr/bin/env python3
"""
形状检测系统连接测试脚本

用于测试与远程形状检测系统的通信是否正常
"""

import asyncio
import logging
import sys
from utils.function_call.vision_arm_control import (
    test_shape_detection_connection,
    shape_get_all_objects,
    shape_get_object_positions
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(name)s: %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

# 设置更详细的日志级别用于调试
logging.getLogger("SHAPE_DETECTION_CONTROL").setLevel(logging.DEBUG)

async def run_tests():
    """运行所有测试函数"""
    
    print("\n" + "="*50)
    print("形状检测系统测试工具")
    print("="*50)
    
    # 测试连接
    print("\n[1] 测试连接到形状检测服务器...")
    result = await test_shape_detection_connection()
    print(f"结果: {result}")
    
    # 如果连接失败，退出测试
    if "失败" in result or "错误" in result or "超时" in result:
        print("\n连接测试失败，无法继续其他测试。请检查网络连接和服务配置。")
        return
    
    # 测试获取所有物体
    print("\n[2] 测试获取所有物体信息...")
    result = await shape_get_all_objects()
    print(f"结果: {result}")
    
    # 测试获取物体位置
    print("\n[3] 测试获取物体位置信息...")
    result = await shape_get_object_positions()
    print(f"结果: {result}")
    
    print("\n"+"="*50)
    print("测试完成")
    print("="*50)

if __name__ == "__main__":
    try:
        # 运行测试
        asyncio.run(run_tests())
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}") 