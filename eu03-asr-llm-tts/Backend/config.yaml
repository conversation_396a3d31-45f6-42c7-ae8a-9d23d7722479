#####################################################################################
# 应用通用配置
app:
  input_mode: "asr" # 可选值: "keyboard", "asr"  （键盘输入或语音输入）


#####################################################################################

# ASR 语音识别配置
asr:
  # 音频输入参数 (PyAudio & sherpa-ncnn)
  sample_rate: 16000        # ASR 模型期望的采样率 (Hz)
  channels: 1               # ASR 模型期望的通道数
  vad_model_path: "./model/vad/silero_vad.onnx"

  # sherpa-ncnn ASR 模型参数
  model_dir: "./model/asr/sherpa-onnx-paraformer-zh-2024-03-09"
  model_files:
    tokens: "tokens.txt"
    paraformer: "model.int8.ort"
  num_threads: 6            # ASR 使用的线程数
  decoding_method: "greedy_search" # 解码方法
  feature_dim: 80

  # 内置端点检测规则参数
  endpoint_rule1_min_trailing_silence: 2.4 # 规则1：尾随静音时间 (秒)
  endpoint_rule2_min_trailing_silence: 1.2 # 规则2：尾随静音时间 (秒)
  endpoint_rule3_min_utterance_length: 0.3 # 规则3：最短有效语音时长 (秒)

  # ALSA 配置 (仅 Linux)
  # alsa_device_name: "hw:0,0" # 指定 ALSA 输入设备名 (运行 arecord -l 查看) - 尝试使用 hw 而不是 plughw

  # 麦克风选择配置
  target_microphone_keyword: "UGREEN" # 指定麦克风名称中包含的关键词，可以通过 arecord -l 查看，例如 "UGREEN" 或 "Yundea"。留空则使用系统默认输入设备。

  # 临时文件输出 (可选，如果需要调试, 主要在 VAD 模式下有用 - 但此处保留以备将来可能切换回)
  output_dir: "./output_wavs_asr" # 保存临时语音段的目录 (如果启用)
  save_temp_wav: false       # 是否保存 VAD 切分的临时 WAV 文件 (True/False)


#####################################################################################


# LLM 大语言模型配置
llm:
  # 火山引擎账号配置
  volcengine:
    api_key: ""
    api_url: "https://ark.cn-beijing.volces.com"

  # 模型配置
  model:
    name: "doubao-1.5-pro-32k-250115"  # 模型名称

  # 推理参数
  parameters:
    temperature: 0.7    # 温度参数，控制随机性
    top_p: 0.95         # Top-p采样参数
    max_tokens: 4096    # 最大输出token数
    stream: true        # 是否使用流式响应

  # 客户端配置
  timeout: 30         # 客户端请求超时时间(秒)
  system_prompt: |

    你是主人的专属AI助手，名叫小U。你需要用俏皮可爱的语气回应主人，回复不应过长，并严格遵循以下规则：
    1.  **工具调用是你的核心能力**：你的主要任务是理解主人的意图，并从你的工具箱（Function Calling）中选择最合适的工具来完成任务。当用户的请求可以通过工具实现时，你必须优先调用工具，绝对不允许直接用自然语言回复"我做不到"或敷衍。
    2.  **理解并匹配工具**：你的工具箱非常强大，包含了本地设备控制（如音乐、机械臂）和通过MCP连接的外部网络服务（如地图导航、天气查询、网络知识检索等）。你需要仔细阅读每个工具的功能描述（description），并选择与用户意图最匹配的一个或多个工具来执行。
    3.  **重要：多轮工具调用的静默模式**：当需要调用多个工具来完成任务时，请在工具调用阶段保持静默，不要输出中间说明文字。直接调用所需的工具，然后在所有工具调用完成后，基于所有工具的结果给出一个完整、连贯的总结回复。例如：
       - 用户："查询深圳天气并计算456+465"
       - 你应该：静默调用天气工具 → 静默调用计算器工具 → 最后输出："主人，深圳明天多云27-33℃，456+465=921哦！"
       - 而不是：输出说明 → 调用工具 → 输出说明 → 调用工具 → 输出总结
    4.  **结果汇报**：工具调用完成后，再用自然语言简要、清晰地向主人汇报结果。如果工具调用失败，请友好地告知主人，并说明原因（如果工具返回了错误信息）。
    5.  **主动澄清**：当用户的指令信息不足以调用工具时（例如，缺少必要的参数），你必须主动向主人提问，获取足够的信息。例如，问路线时需要明确的起点和终点。
    6.  **一步步来**：对于需要多个步骤才能完成的复杂任务，你应该一步步地调用工具，并在每一步完成后向主人汇报进展。

#####################################################################################


# 音频播放配置
audio_player:
  device: null            # 输出设备索引或名称，null为默认（可以使用 aplay -l 查看）
  device_keyword: "pulse" # 设备名称关键词，用于按关键词查找设备，优先级高于device参数
  blocksize: 2048       # 回调帧数 (增加到2048减少underrun)
  buffer_size: 150      # 队列最大缓冲块数 (增加缓冲)
  min_prebuffer: 15     # 最小预缓冲块数 (增加启动前缓冲)
  volume: 0.5           # 默认音量（0.0-1.0）

# 音乐播放器配置
music_player:
  resource_dir: "resource"  # 音乐资源目录路径（相对于项目根目录）
  volume: 0.7                         # 默认音量（0.0-1.0）
  supported_formats: [".mp3", ".wav", ".flac"]  # 支持的音乐文件格式


#####################################################################################


tts:
  # 火山引擎配置
  volcengine:
    app_id: "" # 从 VOLCENGINE_TTS_APP_ID 环境变量加载
    access_token: "" # 从 VOLCENGINE_TTS_ACCESS_TOKEN 环境变量加载
    resource_id: "volc.service_type.10029"
    ws_url: "wss://openspeech.bytedance.com/api/v3/tts/bidirection" # 火山平台双向流式API 地址： `https://www.volcengine.com/docs/6561/1329505` 
    max_size: 1000000000

  # 音频配置
  audio:
    format: "wav"
    sample_rate: 48000 # tts合成音频的采样率

  # 默认音色配置
  voice:
    default_speaker: "zh_female_wanwanxiaohe_moon_bigtts"
    available_speakers:
      - "zh_female_shuangkuaisisi_moon_bigtts"
      - "zh_female_roumeinvyou_emo_v2_mars_bigtts"
      - "zh_female_wanwanxiaohe_moon_bigtts"
      # 可以添加更多音色

  # 会话配置
  session:
    user_id: "1234"
    namespace: "BidirectionalTTS"


#####################################################################################


# 新增：远程 YOLO Demo 控制配置
remote_yolo:
  base_url: "http://***********:5000" # YOLO Demo Flask 服务的地址（替换为板卡B的实际IP）
  timeout: 10.0                        # 请求超时时间 (秒)

#####################################################################################


# 新增：形状检测系统配置
intelligent_vision_arm_control:
  # 形状检测服务的基础URL
  base_url: "http://***********:5002"  # 修改为远程服务器IP地址
  # 请求超时时间（秒）
  timeout: 15.0
  # 连接失败重试次数
  retries: 2
  # 连接错误后的冷却时间（秒）
  cooldown: 30
